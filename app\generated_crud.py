import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Dict, List, Optional, Any
import logging

class SECDBManager:
    """Simple database manager for SEC filing database operations."""
    
    def __init__(self, connection_string: str):
        """
        Initialize database connection.
        
        Args:
            connection_string: PostgreSQL connection string
            Example: "postgresql://user:password@localhost:5432/sec_db"
        """
        self.connection_string = connection_string
        self.conn = None
        self.connect()
    
    def connect(self):
        """Establish database connection."""
        try:
            self.conn = psycopg2.connect(
                self.connection_string,
                cursor_factory=RealDictCursor
            )
            self.conn.autocommit = False
        except Exception as e:
            logging.error(f"Database connection failed: {e}")
            raise
    
    def disconnect(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()
    
    # EXCHANGE OPERATIONS
    def add_exchange(self, exchange_code: str, exchange_name: str, country: str = 'US') -> bool:
        """Add a new exchange."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO exchanges (exchange_code, exchange_name, country)
                    VALUES (%s, %s, %s)
                """, (exchange_code, exchange_name, country))
                self.conn.commit()
                return True
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error adding exchange: {e}")
            return False
    
    def get_exchange(self, exchange_code: str) -> Optional[Dict]:
        """Get exchange by code."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM exchanges WHERE exchange_code = %s", (exchange_code,))
                return dict(cur.fetchone()) if cur.fetchone() else None
        except Exception as e:
            logging.error(f"Error getting exchange: {e}")
            return None
    
    def update_exchange(self, exchange_code: str, **kwargs) -> bool:
        """Update exchange fields."""
        if not kwargs:
            return False
        
        try:
            set_clause = ", ".join([f"{k} = %s" for k in kwargs.keys()])
            values = list(kwargs.values()) + [exchange_code]
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    UPDATE exchanges SET {set_clause}
                    WHERE exchange_code = %s
                """, values)
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error updating exchange: {e}")
            return False
    
    def delete_exchange(self, exchange_code: str) -> bool:
        """Delete exchange."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("DELETE FROM exchanges WHERE exchange_code = %s", (exchange_code,))
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error deleting exchange: {e}")
            return False
    
    # SIC CODE OPERATIONS
    def add_sic_code(self, sic_code: str, office_title: str, industry_title: str, division: str) -> bool:
        """Add a new SIC code."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO sic_codes (sic_code, office_title, industry_title, division)
                    VALUES (%s, %s, %s, %s)
                """, (sic_code, office_title, industry_title, division))
                self.conn.commit()
                return True
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error adding SIC code: {e}")
            return False
    
    def get_sic_code(self, sic_code: str) -> Optional[Dict]:
        """Get SIC code details."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM sic_codes WHERE sic_code = %s", (sic_code,))
                result = cur.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error getting SIC code: {e}")
            return None
    
    def update_sic_code(self, sic_code: str, **kwargs) -> bool:
        """Update SIC code fields."""
        if not kwargs:
            return False
        
        try:
            set_clause = ", ".join([f"{k} = %s" for k in kwargs.keys()])
            values = list(kwargs.values()) + [sic_code]
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    UPDATE sic_codes SET {set_clause}
                    WHERE sic_code = %s
                """, values)
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error updating SIC code: {e}")
            return False
    
    def delete_sic_code(self, sic_code: str) -> bool:
        """Delete SIC code."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("DELETE FROM sic_codes WHERE sic_code = %s", (sic_code,))
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error deleting SIC code: {e}")
            return False
    
    # COMPANY OPERATIONS
    def add_company(self, cik: str, company_name: str, **kwargs) -> bool:
        """Add a new company."""
        try:
            columns = ['cik', 'company_name'] + list(kwargs.keys())
            values = [cik, company_name] + list(kwargs.values())
            placeholders = ', '.join(['%s'] * len(values))
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    INSERT INTO companies ({', '.join(columns)})
                    VALUES ({placeholders})
                """, values)
                self.conn.commit()
                return True
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error adding company: {e}")
            return False
    
    def get_company(self, cik: str) -> Optional[Dict]:
        """Get company by CIK."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM companies WHERE cik = %s", (cik,))
                result = cur.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error getting company: {e}")
            return None
    
    def get_company_by_ticker(self, ticker: str) -> Optional[Dict]:
        """Get company by ticker symbol."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM companies WHERE ticker_symbol = %s", (ticker,))
                result = cur.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error getting company by ticker: {e}")
            return None
    
    def update_company(self, cik: str, **kwargs) -> bool:
        """Update company fields."""
        if not kwargs:
            return False
        
        try:
            set_clause = ", ".join([f"{k} = %s" for k in kwargs.keys()])
            values = list(kwargs.values()) + [cik]
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    UPDATE companies SET {set_clause}
                    WHERE cik = %s
                """, values)
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error updating company: {e}")
            return False
    
    def delete_company(self, cik: str) -> bool:
        """Delete company (and all related filings)."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("DELETE FROM companies WHERE cik = %s", (cik,))
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error deleting company: {e}")
            return False
    
    # FILING OPERATIONS
    def add_filing(self, accession_number: str, submission_type: str, filed_date: str, 
                   company_name: str, company_cik: str, **kwargs) -> Optional[int]:
        """Add a new filing. Returns filing ID if successful."""
        try:
            columns = ['accession_number', 'conformed_submission_type', 'filed_as_of_date', 
                      'company_conformed_name', 'company_cik'] + list(kwargs.keys())
            values = [accession_number, submission_type, filed_date, company_name, company_cik] + list(kwargs.values())
            placeholders = ', '.join(['%s'] * len(values))
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    INSERT INTO filings ({', '.join(columns)})
                    VALUES ({placeholders})
                    RETURNING id
                """, values)
                filing_id = cur.fetchone()['id']
                self.conn.commit()
                return filing_id
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error adding filing: {e}")
            return None
    
    def get_filing(self, filing_id: int) -> Optional[Dict]:
        """Get filing by ID."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM filings WHERE id = %s", (filing_id,))
                result = cur.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error getting filing: {e}")
            return None
    
    def get_filing_by_accession(self, accession_number: str) -> Optional[Dict]:
        """Get filing by accession number."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM filings WHERE accession_number = %s", (accession_number,))
                result = cur.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error getting filing by accession: {e}")
            return None
    
    def get_company_filings(self, cik: str, limit: int = 100) -> List[Dict]:
        """Get filings for a company."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                    SELECT * FROM filings 
                    WHERE company_cik = %s 
                    ORDER BY filed_as_of_date DESC 
                    LIMIT %s
                """, (cik, limit))
                return [dict(row) for row in cur.fetchall()]
        except Exception as e:
            logging.error(f"Error getting company filings: {e}")
            return []
    
    def update_filing(self, filing_id: int, **kwargs) -> bool:
        """Update filing fields."""
        if not kwargs:
            return False
        
        try:
            set_clause = ", ".join([f"{k} = %s" for k in kwargs.keys()])
            values = list(kwargs.values()) + [filing_id]
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    UPDATE filings SET {set_clause}
                    WHERE id = %s
                """, values)
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error updating filing: {e}")
            return False
    
    def delete_filing(self, filing_id: int) -> bool:
        """Delete filing (and all related documents)."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("DELETE FROM filings WHERE id = %s", (filing_id,))
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error deleting filing: {e}")
            return False
    
    # FILING DOCUMENT OPERATIONS
    def add_filing_document(self, filing_id: int, document_type: str, filename: str, **kwargs) -> Optional[int]:
        """Add a new filing document. Returns document ID if successful."""
        try:
            columns = ['filing_id', 'document_type', 'filename'] + list(kwargs.keys())
            values = [filing_id, document_type, filename] + list(kwargs.values())
            placeholders = ', '.join(['%s'] * len(values))
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    INSERT INTO filing_documents ({', '.join(columns)})
                    VALUES ({placeholders})
                    RETURNING id
                """, values)
                doc_id = cur.fetchone()['id']
                self.conn.commit()
                return doc_id
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error adding filing document: {e}")
            return None
    
    def get_filing_document(self, document_id: int) -> Optional[Dict]:
        """Get filing document by ID."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM filing_documents WHERE id = %s", (document_id,))
                result = cur.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logging.error(f"Error getting filing document: {e}")
            return None
    
    def get_filing_documents(self, filing_id: int) -> List[Dict]:
        """Get all documents for a filing."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("SELECT * FROM filing_documents WHERE filing_id = %s", (filing_id,))
                return [dict(row) for row in cur.fetchall()]
        except Exception as e:
            logging.error(f"Error getting filing documents: {e}")
            return []
    
    def update_filing_document(self, document_id: int, **kwargs) -> bool:
        """Update filing document fields."""
        if not kwargs:
            return False
        
        try:
            set_clause = ", ".join([f"{k} = %s" for k in kwargs.keys()])
            values = list(kwargs.values()) + [document_id]
            
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    UPDATE filing_documents SET {set_clause}
                    WHERE id = %s
                """, values)
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error updating filing document: {e}")
            return False
    
    def delete_filing_document(self, document_id: int) -> bool:
        """Delete filing document."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("DELETE FROM filing_documents WHERE id = %s", (document_id,))
                self.conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            self.conn.rollback()
            logging.error(f"Error deleting filing document: {e}")
            return False
    
    # SEARCH OPERATIONS
    def search_companies(self, search_term: str, limit: int = 50) -> List[Dict]:
        """Search companies by name or ticker."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                    SELECT * FROM companies 
                    WHERE company_name ILIKE %s OR ticker_symbol ILIKE %s
                    ORDER BY company_name
                    LIMIT %s
                """, (f"%{search_term}%", f"%{search_term}%", limit))
                return [dict(row) for row in cur.fetchall()]
        except Exception as e:
            logging.error(f"Error searching companies: {e}")
            return []
    
    def search_document_text(self, search_term: str, limit: int = 50) -> List[Dict]:
        """Full-text search in document content."""
        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                    SELECT fd.*, f.accession_number, f.company_conformed_name
                    FROM filing_documents fd
                    JOIN filings f ON fd.filing_id = f.id
                    WHERE to_tsvector('english', fd.document_text) @@ plainto_tsquery('english', %s)
                    ORDER BY ts_rank(to_tsvector('english', fd.document_text), plainto_tsquery('english', %s)) DESC
                    LIMIT %s
                """, (search_term, search_term, limit))
                return [dict(row) for row in cur.fetchall()]
        except Exception as e:
            logging.error(f"Error searching document text: {e}")
            return []


# Example usage
if __name__ == "__main__":
    # Initialize database manager
    db = SECDBManager("postgresql://user:password@localhost:5432/sec_db")
    
    try:
        # Add a company
        success = db.add_company(
            cik="0000012345",
            company_name="Example Corp",
            ticker_symbol="EXAM",
            exchange="NASDAQ",
            sic_code="7372"
        )
        print(f"Company added: {success}")
        
        # Get company
        company = db.get_company("0000012345")
        print(f"Company: {company}")
        
        # Add a filing
        filing_id = db.add_filing(
            accession_number="0000012345-23-000001",
            submission_type="10-K",
            filed_date="2023-03-15",
            company_name="Example Corp",
            company_cik="0000012345"
        )
        print(f"Filing ID: {filing_id}")
        
        # Search companies
        results = db.search_companies("Example")
        print(f"Search results: {len(results)} companies found")
        
    finally:
        db.disconnect()