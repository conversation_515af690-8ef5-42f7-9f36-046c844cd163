import re
from typing import TypedDict
from lxml import html  # type: ignore

def extract_visible_text(html_text: str) -> str:
    try:
        tree = html.fromstring(html_text)

        # Remove invisible elements by tag name
        for tag in tree.xpath('//script | //style | //noscript | //head | //title | //meta'):
            tag.getparent().remove(tag)

        # Remove elements with inline styles that hide them
        for el in tree.xpath('//*[@style]'):
            style = el.attrib.get('style', '').replace(' ', '').lower()
            if 'display:none' in style or 'visibility:hidden' in style:
                parent = el.getparent()
                if parent is not None:
                    parent.remove(el)

        # Extract and return visible text
        text_nodes = tree.xpath('//text()[normalize-space()]')
        return ' '.join(text.strip() for text in text_nodes)

    except Exception as e:
        return f"Error parsing HTML: {e}"

class SECHeader(TypedDict, total=False):
  acceptance_datetime: str
  accession_number: str
  conformed_submission_type: str
  public_document_count: str
  conformed_period_of_report: str
  filing_date: str
  amendment_date: str
  company_conformed_name: str
  cik: str

class SECDocument(TypedDict, total=False):
  document_type: str
  filename: str
  description: str
  text: str

def parse_sec_file(filepath: str) -> tuple[SECHeader, list[SECDocument]]:
  sec_header_patterns = [
    ('acceptance_datetime', re.compile(rb"\s*<ACCEPTANCE-DATETIME>\s*(\d{14})")),
    ('accession_number', re.compile(rb"\s*ACCESSION NUMBER:\s*(\S+)")),
    ('conformed_submission_type', re.compile(rb"\s*CONFORMED SUBMISSION TYPE:\s*(\S+)")),
    ('public_document_count', re.compile(rb"\s*PUBLIC DOCUMENT COUNT:\s*(\d+)")),
    ('conformed_period_of_report', re.compile(rb"\s*CONFORMED PERIOD OF REPORT:\s*(\d{8})")),
    ('filing_date', re.compile(rb"\s*FILED AS OF DATE:\s*(\d{8})")),
    ('amendment_date', re.compile(rb"\s*DATE AS OF CHANGE:\s*(\d{8})")),
    ('company_conformed_name', re.compile(rb"\s*COMPANY CONFORMED NAME:\s*(.+)")),
    ('cik', re.compile(rb"\s*CENTRAL INDEX KEY:\s*(\d+)")),
  ]

  document_patterns = [
    ('document_type', re.compile(rb"\s*<TYPE>\s*(\S+)")),
    ('filename', re.compile(rb"\s*<FILENAME>\s*(\S+)")),
    ('description', re.compile(rb"\s*<DESCRIPTION>\s*(.+)")),
  ]

  document_start_pattern = re.compile(rb"<DOCUMENT>")
  document_end_pattern = re.compile(rb"</DOCUMENT>")

  results: SECHeader = {}
  documents: list[SECDocument] = []

  current_location = 'header'
  in_document = False
  temp_document: SECDocument = {}
  temp_document_text: str = ""

  header_iter = iter(sec_header_patterns)
  doc_iter = iter(document_patterns)

  try:
    header_key, header_pattern = next(header_iter)
  except StopIteration:
    header_key = None
    header_pattern = None

  try:
    doc_key, doc_pattern = next(doc_iter)
  except StopIteration:
    doc_key = None
    doc_pattern = None

  with open(filepath, 'rb') as f:
    for line in f:
      if current_location == 'header' and header_key and header_pattern:
        match = header_pattern.match(line)
        if match:
          results[header_key] = match.group(1).decode()
          try:
            header_key, header_pattern = next(header_iter)
          except StopIteration:
            header_key = None
            header_pattern = None
            current_location = 'documents'

      elif current_location == 'documents':
        if in_document:
          if document_end_pattern.match(line):
            temp_document['text'] = extract_visible_text(temp_document_text)
            documents.append(temp_document)

            temp_document = {}
            in_document = False
            doc_iter = iter(document_patterns)
            try:
              doc_key, doc_pattern = next(doc_iter)
            except StopIteration:
              doc_key = None
              doc_pattern = None
          elif doc_key and doc_pattern:
            match = doc_pattern.match(line)
            if match:
              temp_document[doc_key] = match.group(1).decode()
              try:
                doc_key, doc_pattern = next(doc_iter)
              except StopIteration:
                doc_key = None
                doc_pattern = None
          else:
            temp_document_text = temp_document_text + line.decode()
        elif document_start_pattern.match(line):
          in_document = True
          temp_document = {}

  return results, documents



# Example use
if __name__ == '__main__':
  results, documents = parse_sec_file("a:/DataAggregator/PR/extract/data/CBZ_8-K_20060523_Agreement+Events+Exhibits.txt")
  print(results)
  print(documents[1])
