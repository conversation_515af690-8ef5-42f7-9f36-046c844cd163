services:
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: timescaledb
    restart: unless-stopped
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./wordthing.sql:/docker-entrypoint-initdb.d/wordthing.sql:ro
    networks:
      - app-network

  python-app:
    build: .
    container_name: python-app
    restart: no
    depends_on:
      - timescaledb
    environment:
      DATABASE_URL: ***********************************************/myapp
    volumes:
      - ./app:/app
    working_dir: /app
    networks:
      - app-network

volumes:
  timescaledb_data:

networks:
  app-network:
    driver: bridge